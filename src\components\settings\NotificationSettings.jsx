import React from 'react';
import { Button } from '../ui/button';
import { SettingsSection } from './SettingsSection';

/**
 * 通知设置组件
 * @param {Object} props
 * @param {boolean} props.notifications - 当前通知开启状态
 * @param {Function} props.onToggleNotifications - 切换通知状态的回调函数
 */
export const NotificationSettings = ({ notifications, onToggleNotifications }) => {
  return (
    <SettingsSection title="通知设置">
      <div className="flex items-center justify-between">
        <span>接收系统通知</span>
        <Button
          variant={notifications ? 'default' : 'outline'}
          onClick={onToggleNotifications}
        >
          {notifications ? '已开启' : '已关闭'}
        </Button>
      </div>
    </SettingsSection>
  );
};
