import React from 'react';
import { But<PERSON> } from '../ui/button';
import { SettingsSection } from './SettingsSection';

/**
 * 语言设置组件
 * @param {Object} props
 * @param {string} props.currentLanguage - 当前语言 ('zh-CN' | 'en-US')
 * @param {Function} props.onLanguageChange - 语言变更回调函数
 */
export const LanguageSettings = ({ currentLanguage, onLanguageChange }) => {
  const languages = [
    { value: 'zh-CN', label: '中文' },
    { value: 'en-US', label: 'English' }
  ];

  return (
    <SettingsSection title="语言设置">
      <div className="flex gap-4">
        {languages.map(language => (
          <Button
            key={language.value}
            variant={currentLanguage === language.value ? 'default' : 'outline'}
            onClick={() => onLanguageChange(language.value)}
          >
            {language.label}
          </Button>
        ))}
      </div>
    </SettingsSection>
  );
};
