# 设置页面组件化重构

## 概述
将原本的 `UserSettings.jsx` 页面重构为组件化的实现，提高代码的可维护性和可复用性。

## 重构内容

### 原始实现
- 所有设置功能都在一个大的组件中实现
- 代码耦合度高，难以维护和测试

### 重构后的组件结构

#### 1. `SettingsSection.jsx` - 通用设置区块组件
- **功能**: 提供统一的设置区块布局
- **Props**:
  - `title`: 区块标题
  - `children`: 区块内容
  - `showBorder`: 是否显示顶部边框

#### 2. `PersonalInfoSection.jsx` - 个人信息设置组件
- **功能**: 显示用户的基本信息（用户名、邮箱）
- **Props**:
  - `user`: 用户信息对象

#### 3. `NotificationSettings.jsx` - 通知设置组件
- **功能**: 管理系统通知的开启/关闭
- **Props**:
  - `notifications`: 当前通知状态
  - `onToggleNotifications`: 切换通知状态的回调函数

#### 4. `ThemeSettings.jsx` - 主题设置组件
- **功能**: 管理应用主题（浅色/深色）
- **Props**:
  - `currentTheme`: 当前主题
  - `onThemeChange`: 主题变更回调函数

#### 5. `LanguageSettings.jsx` - 语言设置组件
- **功能**: 管理应用语言（中文/英文）
- **Props**:
  - `currentLanguage`: 当前语言
  - `onLanguageChange`: 语言变更回调函数

## 重构优势

### 1. 代码组织更清晰
- 每个组件职责单一，功能明确
- 便于理解和维护

### 2. 可复用性提高
- 各个设置组件可以在其他页面中复用
- 通用的 `SettingsSection` 组件可用于其他设置页面

### 3. 测试更容易
- 每个组件可以独立测试
- 测试用例更加专注和简洁

### 4. 扩展性更好
- 新增设置项时只需要创建新的组件
- 不会影响现有组件的功能

## 功能保证
重构后的页面功能与原始实现完全一致：
- ✅ 个人信息显示
- ✅ 通知设置切换
- ✅ 主题切换功能
- ✅ 语言切换功能
- ✅ 返回按钮功能

## 使用方式

```jsx
import { 
  PersonalInfoSection, 
  NotificationSettings, 
  ThemeSettings, 
  LanguageSettings 
} from '../components/settings';

// 在页面中使用
<PersonalInfoSection user={user} />
<NotificationSettings 
  notifications={user.settings.notifications}
  onToggleNotifications={handleToggleNotifications}
/>
<ThemeSettings 
  currentTheme={user.settings.theme}
  onThemeChange={handleThemeChange}
/>
<LanguageSettings 
  currentLanguage={user.settings.language}
  onLanguageChange={handleLanguageChange}
/>
```

## 测试文件
为每个组件创建了对应的测试文件：
- `UserSettings.test.jsx` - 整体页面测试
- `ThemeSettings.test.jsx` - 主题设置组件测试
- `NotificationSettings.test.jsx` - 通知设置组件测试
