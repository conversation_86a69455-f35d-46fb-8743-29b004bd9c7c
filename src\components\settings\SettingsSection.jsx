import React from 'react';

/**
 * 通用设置区块组件
 * @param {Object} props
 * @param {string} props.title - 区块标题
 * @param {React.ReactNode} props.children - 区块内容
 * @param {boolean} props.showBorder - 是否显示顶部边框
 */
export const SettingsSection = ({ title, children, showBorder = true }) => {
  return (
    <div className={showBorder ? "border-t pt-6" : ""}>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      {children}
    </div>
  );
};
