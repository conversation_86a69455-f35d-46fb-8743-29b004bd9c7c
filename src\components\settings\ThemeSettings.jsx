import React from 'react';
import { But<PERSON> } from '../ui/button';
import { SettingsSection } from './SettingsSection';

/**
 * 主题设置组件
 * @param {Object} props
 * @param {string} props.currentTheme - 当前主题 ('light' | 'dark')
 * @param {Function} props.onThemeChange - 主题变更回调函数
 */
export const ThemeSettings = ({ currentTheme, onThemeChange }) => {
  const themes = [
    { value: 'light', label: '浅色主题' },
    { value: 'dark', label: '深色主题' }
  ];

  return (
    <SettingsSection title="主题设置">
      <div className="flex gap-4">
        {themes.map(theme => (
          <Button
            key={theme.value}
            variant={currentTheme === theme.value ? 'default' : 'outline'}
            onClick={() => onThemeChange(theme.value)}
          >
            {theme.label}
          </Button>
        ))}
      </div>
    </SettingsSection>
  );
};
