import React from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '../components/ui/button';
import { userStore } from '../store/userStore';
import {
  PersonalInfoSection,
  NotificationSettings,
  ThemeSettings,
  LanguageSettings
} from '../components/settings';

export const UserSettings = observer(() => {
  const { user, updateSettings, setCurrentPage } = userStore;

  const handleToggleNotifications = () => {
    updateSettings({ notifications: !user.settings.notifications });
  };

  const handleThemeChange = (theme) => {
    updateSettings({ theme });
  };

  const handleLanguageChange = (language) => {
    updateSettings({ language });
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-y-auto scrollbar-none bg-gray-50">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-6">用户设置</h2>

        <div className="space-y-6">
          <PersonalInfoSection user={user} />

          <NotificationSettings
            notifications={user.settings.notifications}
            onToggleNotifications={handleToggleNotifications}
          />

          <ThemeSettings
            currentTheme={user.settings.theme}
            onThemeChange={handleThemeChange}
          />

          <LanguageSettings
            currentLanguage={user.settings.language}
            onLanguageChange={handleLanguageChange}
          />
        </div>

        <div className="flex justify-end mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(null)}
          >
            返回
          </Button>
        </div>
      </div>
    </div>
  );
});