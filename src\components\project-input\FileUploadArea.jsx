import React from 'react';
import { FileTextIcon, TrashIcon } from '@radix-ui/react-icons';

/**
 * 文件上传区域组件
 * @param {Object} props
 * @param {Array} props.uploadFiles - 已选择的文件列表
 * @param {Function} props.onFilesChange - 文件变更回调
 * @param {Function} props.onRemoveFile - 移除文件回调
 */
export const FileUploadArea = ({
  uploadFiles = [],
  onFilesChange,
  onRemoveFile
}) => {
  const handleFileSelect = (e) => {
    if (e.target.files?.length) {
      const newFiles = Array.from(e.target.files);
      onFilesChange([...uploadFiles, ...newFiles]);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    onFilesChange([...uploadFiles, ...files]);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.currentTarget.classList.add('border-blue-400');
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('border-blue-400');
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700">上传文件</label>
      <label 
        htmlFor="file-upload"
        className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors duration-200"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="space-y-1 text-center">
          <svg
            className="mx-auto h-8 w-8 text-gray-400"
            stroke="currentColor"
            fill="none"
            viewBox="0 0 48 48"
            aria-hidden="true"
          >
            <path
              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <div className="text-sm text-gray-600">
            <span className="text-blue-600 hover:text-blue-500">选择文件</span>
            <span className="pl-1">或拖拽文件到此处</span>
          </div>
        </div>
      </label>
      
      {/* 隐藏的文件输入框 */}
      <input
        id="file-upload"
        type="file"
        multiple
        className="hidden"
        onChange={handleFileSelect}
      />

      {/* 显示已选择的文件列表 */}
      {uploadFiles.length > 0 && (
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">已选择的文件</label>
          <div className="space-y-2">
            {uploadFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <div className="flex items-center gap-2">
                  <FileTextIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">{file.name}</span>
                </div>
                <button
                  onClick={() => onRemoveFile(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
