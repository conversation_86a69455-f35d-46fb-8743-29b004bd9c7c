import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Cross2Icon } from '@radix-ui/react-icons';
import { ApproverDropdown } from './ApproverDropdown';
import { FileList } from './FileList';

/**
 * 编辑项目输入模态框组件
 * @param {Object} props
 * @param {boolean} props.isOpen - 模态框是否打开
 * @param {Function} props.onClose - 关闭模态框回调
 * @param {Object} props.editingFile - 正在编辑的文件对象
 * @param {Function} props.onEditingFileChange - 编辑文件变更回调
 * @param {Array} props.approvers - 审批人列表
 * @param {boolean} props.loadingApprovers - 审批人加载状态
 * @param {Function} props.onSave - 保存回调
 * @param {Function} props.onPreviewFile - 预览文件回调
 * @param {Function} props.onDownloadFile - 下载文件回调
 */
export const EditProjectInputModal = ({
  isOpen,
  onClose,
  editingFile,
  onEditingFileChange,
  approvers = [],
  loadingApprovers = false,
  onSave,
  onPreviewFile,
  onDownloadFile
}) => {
  const [isApproverDropdownOpen, setIsApproverDropdownOpen] = useState(false);

  if (!isOpen || !editingFile) {
    return null;
  }

  const handleInputChange = (field, value) => {
    onEditingFileChange({
      ...editingFile,
      [field]: value
    });
  };

  const handleApproverChange = (approver) => {
    onEditingFileChange({
      ...editingFile,
      approverName: approver.name,
      approverId: parseInt(approver.id)
    });
  };

  const handleClose = () => {
    setIsApproverDropdownOpen(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[500px]">
        <div className="p-6 border-b flex justify-between items-center">
          <h3 className="text-xl font-semibold">编辑输入</h3>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Cross2Icon className="w-4 h-4" />
          </button>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {/* 文件名称和审核人放在同一行 */}
            <div className="grid grid-cols-2 gap-4">
              {/* 文件名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  输入名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={editingFile.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`mt-1 block w-full rounded-md border ${
                    !editingFile.name ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                {!editingFile.name && (
                  <p className="mt-1 text-sm text-red-500">请输入名称!</p>
                )}
              </div>

              {/* 审核人 */}
              <ApproverDropdown
                value={editingFile.approverName}
                onChange={handleApproverChange}
                approvers={approvers}
                loading={loadingApprovers}
                isOpen={isApproverDropdownOpen}
                onToggle={setIsApproverDropdownOpen}
                required={true}
              />
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700">描述</label>
              <textarea
                value={editingFile.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 min-h-[30px] max-h-[60px]"
                rows="2"
              />
            </div>

            {/* 文件列表 */}
            <FileList
              files={editingFile.files}
              onPreview={onPreviewFile}
              onDownload={onDownloadFile}
              bucketName="projectin"
            />
          </div>
        </div>
        
        <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
          >
            取消
          </Button>
          <Button onClick={onSave}>
            确认修改
          </Button>
        </div>
      </div>
    </div>
  );
};
