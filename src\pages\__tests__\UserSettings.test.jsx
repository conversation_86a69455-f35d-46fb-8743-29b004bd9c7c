import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { UserSettings } from '../UserSettings';
import { userStore } from '../../store/userStore';

// Mock userStore
jest.mock('../../store/userStore', () => ({
  userStore: {
    user: {
      username: 'testuser',
      email: '<EMAIL>',
      settings: {
        notifications: true,
        theme: 'light',
        language: 'zh-CN'
      }
    },
    updateSettings: jest.fn(),
    setCurrentPage: jest.fn()
  }
}));

describe('UserSettings Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders all settings sections', () => {
    render(<UserSettings />);
    
    // 检查页面标题
    expect(screen.getByText('用户设置')).toBeInTheDocument();
    
    // 检查各个设置区块
    expect(screen.getByText('个人信息')).toBeInTheDocument();
    expect(screen.getByText('通知设置')).toBeInTheDocument();
    expect(screen.getByText('主题设置')).toBeInTheDocument();
    expect(screen.getByText('语言设置')).toBeInTheDocument();
  });

  test('displays user information correctly', () => {
    render(<UserSettings />);
    
    // 检查用户名和邮箱是否正确显示
    expect(screen.getByDisplayValue('testuser')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  test('notification toggle works correctly', () => {
    render(<UserSettings />);
    
    // 找到通知开关按钮
    const notificationButton = screen.getByText('已开启');
    expect(notificationButton).toBeInTheDocument();
    
    // 点击按钮
    fireEvent.click(notificationButton);
    
    // 验证 updateSettings 被调用
    expect(userStore.updateSettings).toHaveBeenCalledWith({ notifications: false });
  });

  test('theme change works correctly', () => {
    render(<UserSettings />);
    
    // 找到深色主题按钮
    const darkThemeButton = screen.getByText('深色主题');
    expect(darkThemeButton).toBeInTheDocument();
    
    // 点击按钮
    fireEvent.click(darkThemeButton);
    
    // 验证 updateSettings 被调用
    expect(userStore.updateSettings).toHaveBeenCalledWith({ theme: 'dark' });
  });

  test('language change works correctly', () => {
    render(<UserSettings />);
    
    // 找到英文语言按钮
    const englishButton = screen.getByText('English');
    expect(englishButton).toBeInTheDocument();
    
    // 点击按钮
    fireEvent.click(englishButton);
    
    // 验证 updateSettings 被调用
    expect(userStore.updateSettings).toHaveBeenCalledWith({ language: 'en-US' });
  });

  test('return button works correctly', () => {
    render(<UserSettings />);
    
    // 找到返回按钮
    const returnButton = screen.getByText('返回');
    expect(returnButton).toBeInTheDocument();
    
    // 点击按钮
    fireEvent.click(returnButton);
    
    // 验证 setCurrentPage 被调用
    expect(userStore.setCurrentPage).toHaveBeenCalledWith(null);
  });
});
