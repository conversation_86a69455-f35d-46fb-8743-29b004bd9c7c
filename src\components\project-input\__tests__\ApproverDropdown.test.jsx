import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ApproverDropdown } from '../ApproverDropdown';

const mockApprovers = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
];

const defaultProps = {
  value: '',
  onChange: jest.fn(),
  approvers: mockApprovers,
  loading: false,
  isOpen: false,
  onToggle: jest.fn(),
  required: true,
  placeholder: '请选择审核人'
};

describe('ApproverDropdown Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders dropdown with placeholder when no value selected', () => {
    render(<ApproverDropdown {...defaultProps} />);
    
    expect(screen.getByText('审核人')).toBeInTheDocument();
    expect(screen.getByText('请选择审核人')).toBeInTheDocument();
    expect(screen.getByText('*')).toBeInTheDocument(); // required indicator
  });

  test('renders dropdown with selected value', () => {
    render(<ApproverDropdown {...defaultProps} value="张三" />);
    
    expect(screen.getByText('张三')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    render(<ApproverDropdown {...defaultProps} loading={true} />);
    
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  test('calls onToggle when dropdown is clicked', () => {
    render(<ApproverDropdown {...defaultProps} />);
    
    const dropdown = screen.getByText('请选择审核人').closest('div');
    fireEvent.click(dropdown);
    
    expect(defaultProps.onToggle).toHaveBeenCalledWith(true);
  });

  test('displays approver options when dropdown is open', () => {
    render(<ApproverDropdown {...defaultProps} isOpen={true} />);
    
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('李四')).toBeInTheDocument();
    expect(screen.getByText('王五')).toBeInTheDocument();
  });

  test('calls onChange when approver is selected', () => {
    render(<ApproverDropdown {...defaultProps} isOpen={true} />);
    
    const approverOption = screen.getByText('张三');
    fireEvent.click(approverOption);
    
    expect(defaultProps.onChange).toHaveBeenCalledWith({ id: 1, name: '张三' });
  });

  test('shows validation error when required and no value', () => {
    render(<ApproverDropdown {...defaultProps} required={true} value="" />);
    
    expect(screen.getByText('请选择审核人!')).toBeInTheDocument();
  });

  test('does not show validation error when not required', () => {
    render(<ApproverDropdown {...defaultProps} required={false} value="" />);
    
    expect(screen.queryByText('请选择审核人!')).not.toBeInTheDocument();
  });

  test('does not open dropdown when loading', () => {
    render(<ApproverDropdown {...defaultProps} loading={true} />);
    
    const dropdown = screen.getByText('加载中...').closest('div');
    fireEvent.click(dropdown);
    
    expect(defaultProps.onToggle).not.toHaveBeenCalled();
  });
});
