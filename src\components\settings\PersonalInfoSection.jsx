import React from 'react';
import { SettingsSection } from './SettingsSection';

/**
 * 个人信息设置组件
 * @param {Object} props
 * @param {Object} props.user - 用户信息对象
 * @param {string} props.user.username - 用户名
 * @param {string} props.user.email - 邮箱
 */
export const PersonalInfoSection = ({ user }) => {
  return (
    <SettingsSection title="个人信息" showBorder={false}>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            用户名
          </label>
          <input
            type="text"
            value={user.username}
            disabled
            className="w-full px-3 py-2 border rounded-md bg-gray-50"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            邮箱
          </label>
          <input
            type="email"
            value={user.email}
            disabled
            className="w-full px-3 py-2 border rounded-md bg-gray-50"
          />
        </div>
      </div>
    </SettingsSection>
  );
};
