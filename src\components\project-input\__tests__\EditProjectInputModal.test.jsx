import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { EditProjectInputModal } from '../EditProjectInputModal';

// Mock 数据
const mockEditingFile = {
  id: 1,
  name: '测试文件',
  description: '测试描述',
  approverName: '张三',
  approverId: 1,
  files: [
    { id: 1, name: 'test.pdf' },
    { id: 2, name: 'document.docx' }
  ]
};

const mockApprovers = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
];

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  editingFile: mockEditingFile,
  onEditingFileChange: jest.fn(),
  approvers: mockApprovers,
  loadingApprovers: false,
  onSave: jest.fn(),
  onPreviewFile: jest.fn(),
  onDownloadFile: jest.fn()
};

describe('EditProjectInputModal Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders modal when isOpen is true', () => {
    render(<EditProjectInputModal {...defaultProps} />);
    
    expect(screen.getByText('编辑输入')).toBeInTheDocument();
    expect(screen.getByDisplayValue('测试文件')).toBeInTheDocument();
    expect(screen.getByDisplayValue('测试描述')).toBeInTheDocument();
  });

  test('does not render modal when isOpen is false', () => {
    render(<EditProjectInputModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('编辑输入')).not.toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    render(<EditProjectInputModal {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  test('calls onSave when save button is clicked', () => {
    render(<EditProjectInputModal {...defaultProps} />);
    
    const saveButton = screen.getByText('确认修改');
    fireEvent.click(saveButton);
    
    expect(defaultProps.onSave).toHaveBeenCalledTimes(1);
  });

  test('updates file name when input changes', () => {
    render(<EditProjectInputModal {...defaultProps} />);
    
    const nameInput = screen.getByDisplayValue('测试文件');
    fireEvent.change(nameInput, { target: { value: '新文件名' } });
    
    expect(defaultProps.onEditingFileChange).toHaveBeenCalledWith({
      ...mockEditingFile,
      name: '新文件名'
    });
  });

  test('displays file list when files exist', () => {
    render(<EditProjectInputModal {...defaultProps} />);
    
    expect(screen.getByText('已有文件')).toBeInTheDocument();
    expect(screen.getByText('test.pdf')).toBeInTheDocument();
    expect(screen.getByText('document.docx')).toBeInTheDocument();
  });

  test('shows validation error when name is empty', () => {
    const editingFileWithoutName = { ...mockEditingFile, name: '' };
    
    render(
      <EditProjectInputModal 
        {...defaultProps} 
        editingFile={editingFileWithoutName} 
      />
    );
    
    expect(screen.getByText('请输入名称!')).toBeInTheDocument();
  });
});
