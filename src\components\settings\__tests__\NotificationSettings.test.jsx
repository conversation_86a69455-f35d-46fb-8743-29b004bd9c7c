import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { NotificationSettings } from '../NotificationSettings';

describe('NotificationSettings Component', () => {
  const mockOnToggleNotifications = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders notification settings correctly', () => {
    render(
      <NotificationSettings 
        notifications={true} 
        onToggleNotifications={mockOnToggleNotifications} 
      />
    );
    
    expect(screen.getByText('通知设置')).toBeInTheDocument();
    expect(screen.getByText('接收系统通知')).toBeInTheDocument();
    expect(screen.getByText('已开启')).toBeInTheDocument();
  });

  test('shows correct status when notifications are disabled', () => {
    render(
      <NotificationSettings 
        notifications={false} 
        onToggleNotifications={mockOnToggleNotifications} 
      />
    );
    
    expect(screen.getByText('已关闭')).toBeInTheDocument();
  });

  test('calls onToggleNotifications when button is clicked', () => {
    render(
      <NotificationSettings 
        notifications={true} 
        onToggleNotifications={mockOnToggleNotifications} 
      />
    );
    
    const toggleButton = screen.getByText('已开启');
    fireEvent.click(toggleButton);
    
    expect(mockOnToggleNotifications).toHaveBeenCalledTimes(1);
  });
});
