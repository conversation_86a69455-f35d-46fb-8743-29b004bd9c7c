import React, { useRef, useEffect } from 'react';

/**
 * 审批人下拉选择组件
 * @param {Object} props
 * @param {string} props.value - 当前选中的审批人名称
 * @param {Function} props.onChange - 选择变更回调函数
 * @param {Array} props.approvers - 审批人列表
 * @param {boolean} props.loading - 加载状态
 * @param {boolean} props.isOpen - 下拉框是否打开
 * @param {Function} props.onToggle - 切换下拉框状态
 * @param {boolean} props.required - 是否必填
 * @param {string} props.placeholder - 占位符文本
 * @param {boolean} props.touched - 是否已触摸（用于验证显示）
 * @param {Function} props.onBlur - 失焦回调
 */
export const ApproverDropdown = ({
  value,
  onChange,
  approvers = [],
  loading = false,
  isOpen,
  onToggle,
  required = false,
  placeholder = '请选择审核人',
  touched = false,
  onBlur
}) => {
  const dropdownRef = useRef(null);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onToggle(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onToggle]);

  const handleApproverSelect = (approver) => {
    onChange(approver);
    onToggle(false);
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700">
        审核人 {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative" ref={dropdownRef}>
        <div
          onClick={() => !loading && onToggle(!isOpen)}
          onBlur={() => onBlur && onBlur()}
          className={`mt-1 w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
            touched && !value && required ? 'border-red-300' : 'border-gray-300'
          } ${loading ? 'opacity-50 cursor-not-allowed' : ''} hover:border-blue-500`}
        >
          <span className={value ? 'text-gray-900' : 'text-gray-400'}>
            {loading ? '加载中...' : (value || placeholder)}
          </span>
          <div className="flex items-center">
            {loading ? (
              <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </div>
        
        {isOpen && !loading && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="py-1 max-h-60 overflow-auto">
              {approvers.map(approver => (
                <div
                  key={approver.id}
                  onClick={() => handleApproverSelect(approver)}
                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                    value === approver.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                  }`}
                >
                  {approver.name}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      {touched && !value && required && (
        <p className="mt-1 text-sm text-red-500">请选择审核人!</p>
      )}
    </div>
  );
};
