import React, { useRef, useEffect } from 'react';

/**
 * 状态下拉选择组件
 * @param {Object} props
 * @param {string} props.value - 当前选中的状态值
 * @param {Function} props.onChange - 选择变更回调函数
 * @param {Array} props.options - 状态选项列表
 * @param {boolean} props.isOpen - 下拉框是否打开
 * @param {Function} props.onToggle - 切换下拉框状态
 * @param {string} props.placeholder - 占位符文本
 */
export const StatusDropdown = ({
  value,
  onChange,
  options = [],
  isOpen,
  onToggle,
  placeholder = '请选择状态'
}) => {
  const dropdownRef = useRef(null);

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onToggle(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onToggle]);

  const handleStatusSelect = (option) => {
    onChange(option.value);
    onToggle(false);
  };

  const selectedOption = options.find(option => option.value === value);

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700">状态</label>
      <div className="relative" ref={dropdownRef}>
        <div
          onClick={() => onToggle(!isOpen)}
          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-blue-500"
        >
          <span className={value ? 'text-gray-900' : 'text-gray-400'}>
            {selectedOption?.label || placeholder}
          </span>
          <svg 
            className={`h-5 w-5 text-gray-400 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
              clipRule="evenodd" 
            />
          </svg>
        </div>
        
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="py-1 max-h-60 overflow-auto">
              {options.map(option => (
                <div
                  key={option.value}
                  onClick={() => handleStatusSelect(option)}
                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                    value === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                  }`}
                >
                  {option.label}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
