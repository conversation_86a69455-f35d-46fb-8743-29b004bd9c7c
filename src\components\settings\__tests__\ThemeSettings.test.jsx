import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeSettings } from '../ThemeSettings';

describe('ThemeSettings Component', () => {
  const mockOnThemeChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders theme settings correctly', () => {
    render(
      <ThemeSettings 
        currentTheme="light" 
        onThemeChange={mockOnThemeChange} 
      />
    );
    
    expect(screen.getByText('主题设置')).toBeInTheDocument();
    expect(screen.getByText('浅色主题')).toBeInTheDocument();
    expect(screen.getByText('深色主题')).toBeInTheDocument();
  });

  test('highlights current theme correctly', () => {
    render(
      <ThemeSettings 
        currentTheme="dark" 
        onThemeChange={mockOnThemeChange} 
      />
    );
    
    const lightButton = screen.getByText('浅色主题');
    const darkButton = screen.getByText('深色主题');
    
    // 深色主题按钮应该有 default variant（被选中状态）
    expect(darkButton.closest('button')).toHaveClass('bg-gray-900');
    expect(lightButton.closest('button')).not.toHaveClass('bg-gray-900');
  });

  test('calls onThemeChange when theme is selected', () => {
    render(
      <ThemeSettings 
        currentTheme="light" 
        onThemeChange={mockOnThemeChange} 
      />
    );
    
    const darkButton = screen.getByText('深色主题');
    fireEvent.click(darkButton);
    
    expect(mockOnThemeChange).toHaveBeenCalledWith('dark');
  });
});
