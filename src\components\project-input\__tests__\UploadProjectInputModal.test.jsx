import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { UploadProjectInputModal } from '../UploadProjectInputModal';

// Mock 数据
const mockFormData = {
  name: '测试项目输入',
  uploadTime: '2024-01-01',
  approver: '张三',
  status: 'DRAFT',
  description: '测试描述'
};

const mockApprovers = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' }
];

const mockStatusOptions = [
  { value: 'DRAFT', label: '草稿' },
  { value: 'PENDING', label: '待审批' },
  { value: 'APPROVED', label: '已通过' },
  { value: 'REJECTED', label: '已拒绝' }
];

const mockUploadFiles = [
  new File(['test content'], 'test1.pdf', { type: 'application/pdf' }),
  new File(['test content'], 'test2.docx', { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
];

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  formData: mockFormData,
  onFormDataChange: jest.fn(),
  uploadFiles: mockUploadFiles,
  onUploadFilesChange: jest.fn(),
  approvers: mockApprovers,
  loadingApprovers: false,
  statusOptions: mockStatusOptions,
  onSave: jest.fn(),
  onRemoveFile: jest.fn()
};

describe('UploadProjectInputModal Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders modal when isOpen is true', () => {
    render(<UploadProjectInputModal {...defaultProps} />);
    
    expect(screen.getByText('项目输入')).toBeInTheDocument();
    expect(screen.getByDisplayValue('测试项目输入')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2024-01-01')).toBeInTheDocument();
    expect(screen.getByDisplayValue('测试描述')).toBeInTheDocument();
  });

  test('does not render modal when isOpen is false', () => {
    render(<UploadProjectInputModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('项目输入')).not.toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    render(<UploadProjectInputModal {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  test('calls onSave when save button is clicked', () => {
    render(<UploadProjectInputModal {...defaultProps} />);
    
    const saveButton = screen.getByText('确认上传');
    fireEvent.click(saveButton);
    
    expect(defaultProps.onSave).toHaveBeenCalledTimes(1);
  });

  test('updates form data when input changes', () => {
    render(<UploadProjectInputModal {...defaultProps} />);
    
    const nameInput = screen.getByDisplayValue('测试项目输入');
    fireEvent.change(nameInput, { target: { value: '新项目名称' } });
    
    expect(defaultProps.onFormDataChange).toHaveBeenCalledWith({
      ...mockFormData,
      name: '新项目名称'
    });
  });

  test('displays upload files when files exist', () => {
    render(<UploadProjectInputModal {...defaultProps} />);
    
    expect(screen.getByText('已选择的文件')).toBeInTheDocument();
    expect(screen.getByText('test1.pdf')).toBeInTheDocument();
    expect(screen.getByText('test2.docx')).toBeInTheDocument();
  });

  test('shows loading state for approvers', () => {
    render(
      <UploadProjectInputModal
        {...defaultProps}
        loadingApprovers={true}
      />
    );

    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  test('shows validation errors only after user interaction', () => {
    const emptyFormData = {
      name: '',
      uploadTime: '2024-01-01',
      approver: '',
      status: 'DRAFT',
      description: ''
    };

    render(
      <UploadProjectInputModal
        {...defaultProps}
        formData={emptyFormData}
      />
    );

    // 初始状态不应该显示错误提示
    expect(screen.queryByText('请输入名称!')).not.toBeInTheDocument();
    expect(screen.queryByText('请选择审核人!')).not.toBeInTheDocument();

    // 用户交互后才显示错误提示
    const nameInput = screen.getByDisplayValue('');
    fireEvent.blur(nameInput);

    expect(screen.getByText('请输入名称!')).toBeInTheDocument();
  });
});
