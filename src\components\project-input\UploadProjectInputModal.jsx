import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Cross2Icon } from '@radix-ui/react-icons';
import { ApproverDropdown } from './ApproverDropdown';
import { StatusDropdown } from './StatusDropdown';
import { FileUploadArea } from './FileUploadArea';

/**
 * 上传项目输入模态框组件
 * @param {Object} props
 * @param {boolean} props.isOpen - 模态框是否打开
 * @param {Function} props.onClose - 关闭模态框回调
 * @param {Object} props.formData - 表单数据
 * @param {Function} props.onFormDataChange - 表单数据变更回调
 * @param {Array} props.uploadFiles - 上传文件列表
 * @param {Function} props.onUploadFilesChange - 上传文件变更回调
 * @param {Array} props.approvers - 审批人列表
 * @param {boolean} props.loadingApprovers - 审批人加载状态
 * @param {Array} props.statusOptions - 状态选项列表
 * @param {Function} props.onSave - 保存回调
 * @param {Function} props.onRemoveFile - 移除文件回调
 */
export const UploadProjectInputModal = ({
  isOpen,
  onClose,
  formData,
  onFormDataChange,
  uploadFiles,
  onUploadFilesChange,
  approvers = [],
  loadingApprovers = false,
  statusOptions = [],
  onSave,
  onRemoveFile
}) => {
  const [isApproverDropdownOpen, setIsApproverDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [touched, setTouched] = useState({
    name: false,
    approver: false
  });

  if (!isOpen) {
    return null;
  }

  const handleInputChange = (field, value) => {
    onFormDataChange({
      ...formData,
      [field]: value
    });
    // 标记字段为已触摸
    setTouched(prev => ({
      ...prev,
      [field]: true
    }));
  };

  const handleApproverChange = (approver) => {
    onFormDataChange({
      ...formData,
      approver: approver.name
    });
    // 标记审核人字段为已触摸
    setTouched(prev => ({
      ...prev,
      approver: true
    }));
  };

  const handleStatusChange = (status) => {
    onFormDataChange({
      ...formData,
      status: status
    });
  };

  const handleClose = () => {
    setIsApproverDropdownOpen(false);
    setIsStatusDropdownOpen(false);
    // 重置触摸状态
    setTouched({
      name: false,
      approver: false
    });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[600px]">
        <div className="p-6 border-b flex justify-between items-center">
          <h3 className="text-xl font-semibold">项目输入</h3>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Cross2Icon className="w-4 h-4" />
          </button>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {/* 文件名和上传时间放在同一行 */}
            <div className="grid grid-cols-2 gap-4">
              {/* 文件名 */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  输入名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  onBlur={() => setTouched(prev => ({ ...prev, name: true }))}
                  className={`mt-1 block w-full rounded-md border ${
                    touched.name && !formData.name ? 'border-red-300' : 'border-gray-300'
                  } px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200`}
                  required
                />
                {touched.name && !formData.name && (
                  <p className="mt-1 text-sm text-red-500">请输入名称!</p>
                )}
              </div>

              {/* 上传时间 */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  创建时间 <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  value={formData.uploadTime || ''}
                  onChange={(e) => handleInputChange('uploadTime', e.target.value)}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 cursor-pointer focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200"
                  required
                />
              </div>
            </div>

            {/* 审核人和状态放在同一行 */}
            <div className="grid grid-cols-2 gap-4">
              {/* 审核人 */}
              <ApproverDropdown
                value={formData.approver}
                onChange={handleApproverChange}
                approvers={approvers}
                loading={loadingApprovers}
                isOpen={isApproverDropdownOpen}
                onToggle={setIsApproverDropdownOpen}
                required={true}
                touched={touched.approver}
                onBlur={() => setTouched(prev => ({ ...prev, approver: true }))}
              />

              {/* 状态 */}
              <StatusDropdown
                value={formData.status}
                onChange={handleStatusChange}
                options={statusOptions}
                isOpen={isStatusDropdownOpen}
                onToggle={setIsStatusDropdownOpen}
              />
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700">描述</label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 min-h-[30px] max-h-[60px] focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200"
                rows="2"
              />
            </div>

            {/* 文件上传区域 */}
            <FileUploadArea
              uploadFiles={uploadFiles}
              onFilesChange={onUploadFilesChange}
              onRemoveFile={onRemoveFile}
            />
          </div>
        </div>
        
        <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
          >
            取消
          </Button>
          <Button onClick={onSave}>
            确认上传
          </Button>
        </div>
      </div>
    </div>
  );
};
