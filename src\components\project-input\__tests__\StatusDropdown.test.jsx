import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { StatusDropdown } from '../StatusDropdown';

const mockStatusOptions = [
  { value: 'DRAFT', label: '草稿' },
  { value: 'PENDING', label: '待审批' },
  { value: 'APPROVED', label: '已通过' },
  { value: 'REJECTED', label: '已拒绝' }
];

const defaultProps = {
  value: '',
  onChange: jest.fn(),
  options: mockStatusOptions,
  isOpen: false,
  onToggle: jest.fn(),
  placeholder: '请选择状态'
};

describe('StatusDropdown Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders dropdown with placeholder when no value selected', () => {
    render(<StatusDropdown {...defaultProps} />);
    
    expect(screen.getByText('状态')).toBeInTheDocument();
    expect(screen.getByText('请选择状态')).toBeInTheDocument();
  });

  test('renders dropdown with selected value', () => {
    render(<StatusDropdown {...defaultProps} value="DRAFT" />);
    
    expect(screen.getByText('草稿')).toBeInTheDocument();
  });

  test('calls onToggle when dropdown is clicked', () => {
    render(<StatusDropdown {...defaultProps} />);
    
    const dropdown = screen.getByText('请选择状态').closest('div');
    fireEvent.click(dropdown);
    
    expect(defaultProps.onToggle).toHaveBeenCalledWith(true);
  });

  test('displays status options when dropdown is open', () => {
    render(<StatusDropdown {...defaultProps} isOpen={true} />);
    
    expect(screen.getByText('草稿')).toBeInTheDocument();
    expect(screen.getByText('待审批')).toBeInTheDocument();
    expect(screen.getByText('已通过')).toBeInTheDocument();
    expect(screen.getByText('已拒绝')).toBeInTheDocument();
  });

  test('calls onChange when status is selected', () => {
    render(<StatusDropdown {...defaultProps} isOpen={true} />);
    
    const statusOption = screen.getByText('草稿');
    fireEvent.click(statusOption);
    
    expect(defaultProps.onChange).toHaveBeenCalledWith('DRAFT');
  });

  test('highlights selected status correctly', () => {
    render(<StatusDropdown {...defaultProps} value="PENDING" isOpen={true} />);
    
    const selectedOption = screen.getByText('待审批').closest('div');
    expect(selectedOption).toHaveClass('bg-blue-50', 'text-blue-600');
  });

  test('shows correct arrow rotation when open', () => {
    const { rerender } = render(<StatusDropdown {...defaultProps} isOpen={false} />);
    
    let arrow = screen.getByRole('img', { hidden: true });
    expect(arrow).not.toHaveClass('rotate-180');
    
    rerender(<StatusDropdown {...defaultProps} isOpen={true} />);
    arrow = screen.getByRole('img', { hidden: true });
    expect(arrow).toHaveClass('rotate-180');
  });
});
