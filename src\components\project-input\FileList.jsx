import React from 'react';
import { Button } from '../ui/button';
import { FileTextIcon, EyeOpenIcon, DownloadIcon } from '@radix-ui/react-icons';

/**
 * 文件列表组件
 * @param {Object} props
 * @param {Array} props.files - 文件列表
 * @param {Function} props.onPreview - 预览文件回调
 * @param {Function} props.onDownload - 下载文件回调
 * @param {string} props.bucketName - 存储桶名称
 * @param {boolean} props.showActions - 是否显示操作按钮
 */
export const FileList = ({ 
  files = [], 
  onPreview, 
  onDownload, 
  bucketName = 'projectin',
  showActions = true 
}) => {
  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">已有文件</label>
      <div className="max-h-[100px] overflow-y-auto pr-2">
        <div className="space-y-2">
          {files.map(file => (
            <div key={file.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
              <div className="flex items-center gap-2">
                <FileTextIcon className="w-4 h-4 text-gray-400" />
                <span className="text-sm">{file.name}</span>
              </div>
              {showActions && (
                <div className="flex items-center gap-2">
                  {/* 预览按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-600 hover:text-blue-700"
                    onClick={() => onPreview && onPreview(file, bucketName)}
                  >
                    <EyeOpenIcon className="w-4 h-4" />
                  </Button>

                  {/* 下载按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-green-600 hover:text-green-700"
                    onClick={() => onDownload && onDownload(file, bucketName)}
                  >
                    <DownloadIcon className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
